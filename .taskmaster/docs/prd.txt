# Product Requirements Document: EduSG - AI-Powered Educational Content Platform

# Overview
EduSG is a sophisticated AI-powered educational content generation platform designed specifically for the Singapore education system. The platform generates curriculum-aligned exercises, assessments, and educational materials using advanced AI technologies including OpenAI, Google Generative AI, and LlamaIndex. The system supports multiple question types, integrates with vector databases for semantic search, implements school-specific examination formats, and provides real-time collaboration capabilities for educators.

# Core Features

## 1. AI-Powered Content Generation
- Generate curriculum-aligned exercises using multiple AI providers (OpenAI, Google AI, LlamaIndex)
- Support for multiple question types: multiple choice, fill-in-blank, creative writing, short answer
- Implement cognitive level distribution (Knowledge, Understanding, Application, Analysis)
- Age-appropriate content validation and cultural sensitivity checks
- Mathematical content support with MathML formatting and geometric diagrams

## 2. Vector Database Integration
- Semantic search capabilities using Qdrant and Pinecone vector databases
- Query expansion for improved search results
- Embedding strategies optimized for educational content
- Curriculum content retrieval and matching

## 3. Educational Content Architecture
- Subject hierarchy management: topic → parentSubject → subject
- Multiple difficulty levels and cognitive complexity handling
- School-specific examination format parsing and analysis
- Narrative structure extraction from sample questions
- Image generation with precise mathematical specifications

## 4. Real-time Collaboration
- WebSocket connections for live collaboration
- Real-time content generation and updates
- Concurrent user session management
- State synchronization across multiple users

## 5. Background Processing
- BullMQ integration for background job processing
- Asynchronous content generation workflows
- Queue management for worksheet generation
- Progress tracking and status updates

## 6. Question Pool Management
- Question storage and retrieval system
- Random question selection algorithms
- Quality assurance and validation
- Hybrid approach combining pool selection and AI generation

# User Experience

## User Personas
- **Teachers**: Create worksheets, assessments, and educational materials
- **Content Administrators**: Manage curriculum standards and question pools
- **System Administrators**: Monitor system performance and manage configurations

## Key User Flows
- Document upload and processing workflow
- Worksheet generation with AI-powered content
- Question pool management and selection
- Real-time collaboration sessions
- Assessment creation and customization

## UI/UX Considerations
- Intuitive content generation interface
- Real-time progress indicators for AI processing
- Responsive design for various devices
- Accessibility compliance for educational environments

# Technical Architecture

## System Components
- **Backend Framework**: NestJS 11.0.1 with TypeScript 5.7.3
- **Databases**: MongoDB (Mongoose 8.13.2), TypeORM 0.3.22
- **Vector Databases**: Qdrant, Pinecone for semantic search
- **AI Services**: OpenAI 4.94.0, Google Generative AI 0.24.0, LlamaIndex 0.9.19
- **Real-time Communication**: Socket.IO 4.8.1, WebSockets
- **Background Processing**: BullMQ 5.49.2
- **Testing**: Jest 29.7.0, Supertest 7.0.0

## Data Models
- User management and authentication
- Educational content hierarchy (subjects, topics, questions)
- Question pool with metadata and relationships
- Worksheet templates and generated content
- Assessment results and analytics

## APIs and Integrations
- RESTful APIs for content management
- WebSocket APIs for real-time features
- AI service integrations with fallback mechanisms
- Vector database APIs for semantic search
- Image generation APIs for visual content

## Infrastructure Requirements
- Scalable cloud deployment (containerized with Docker)
- Redis for caching and session management
- File storage for generated content and images
- Monitoring and logging systems
- Security measures for API keys and sensitive data

# Development Roadmap

## Phase 1: Core Infrastructure (MVP)
- Set up NestJS application structure with TypeScript
- Implement basic user authentication and authorization
- Configure database connections (MongoDB, TypeORM)
- Set up AI service integrations (OpenAI, Google AI)
- Basic content generation API endpoints
- Simple question creation and storage

## Phase 2: Content Generation Engine
- Implement Strategy Pattern for topic-specific instructions
- Build prompt engineering system with context-aware generation
- Develop question type factories (multiple choice, fill-blank, etc.)
- Create educational content validation system
- Implement basic worksheet generation workflow
- Add support for mathematical content and MathML

## Phase 3: Vector Database Integration
- Integrate Qdrant and Pinecone vector databases
- Implement semantic search capabilities
- Build query expansion algorithms
- Create embedding strategies for educational content
- Develop curriculum content retrieval system

## Phase 4: Advanced Features
- Implement real-time collaboration with WebSockets
- Build background job processing with BullMQ
- Create question pool management system
- Develop random selection algorithms
- Add image generation capabilities
- Implement school-specific format parsing

## Phase 5: Quality Assurance & Optimization
- Comprehensive testing suite (unit, integration, e2e)
- Performance optimization and caching strategies
- Advanced error handling and monitoring
- Security hardening and API rate limiting
- Documentation and deployment automation

## Phase 6: Enhanced Collaboration & Analytics
- Advanced real-time collaboration features
- Analytics and reporting dashboard
- Advanced customization options
- Multi-language support
- Mobile-responsive interface improvements

# Logical Dependency Chain

## Foundation Layer (Must be built first)
1. NestJS application setup with TypeScript configuration
2. Database connections and basic models
3. User authentication and authorization system
4. AI service integrations with basic error handling

## Core Functionality Layer
1. Content generation engine with prompt building
2. Question type strategies and factories
3. Basic worksheet generation workflow
4. Educational content validation system

## Advanced Features Layer
1. Vector database integration for semantic search
2. Background job processing system
3. Real-time collaboration infrastructure
4. Question pool management system

## Optimization Layer
1. Performance optimization and caching
2. Advanced error handling and monitoring
3. Security enhancements
4. Comprehensive testing coverage

# Risks and Mitigations

## Technical Challenges
- **AI Service Reliability**: Implement fallback mechanisms and retry logic
- **Vector Database Performance**: Optimize queries and implement caching
- **Real-time Scalability**: Use Redis for session management and load balancing
- **Content Quality**: Implement validation algorithms and human review processes

## MVP Scope Management
- Focus on core content generation before advanced features
- Implement basic question types before complex mathematical content
- Start with single-user workflows before real-time collaboration
- Prioritize stability over feature completeness in early phases

## Resource Constraints
- **API Costs**: Implement usage monitoring and optimization
- **Development Time**: Use established patterns and frameworks
- **Testing Complexity**: Implement comprehensive mocking for AI services
- **Deployment Complexity**: Use containerization and infrastructure as code

# Appendix

## Research Findings
- Singapore curriculum standards and requirements
- Educational content generation best practices
- AI prompt engineering for educational content
- Vector database performance benchmarks

## Technical Specifications
- Node.js runtime requirements
- Database schema designs
- API endpoint specifications
- WebSocket event definitions
- AI service integration patterns

## Success Metrics
- Content generation accuracy and relevance
- System response times and performance
- User adoption and engagement rates
- Educational outcome improvements
- System reliability and uptime