# Task Parsing Workflow for EduSG Educational Platform

This document outlines the comprehensive workflow for parsing tasks from the Product Requirements Document (PRD) and generating consistent, actionable development tasks based on the EduSG backend codebase structure and established patterns.

## 1. INPUT SOURCES & CONTEXT

### Primary Sources
- **Product Requirements Document (PRD)**: `.taskmaster/docs/prd.txt` - Main feature specifications
- **Codebase Analysis**: `/Volumes/500GB/TechVSI/edusg-be/` - Existing project structure and patterns
- **Memory Bank**: EduSG project documentation including:
  - `codebase-be-context.md` - Comprehensive backend architecture overview
  - `development-rule-be.md` - Coding standards and patterns
  - Feature-specific documentation (e.g., random question pool selection)

### Context Understanding
- **Technology Stack**: NestJS 11.0.1, TypeScript 5.7.3, PostgreSQL, MongoDB, Redis
- **Architecture Patterns**: Strategy Pattern, Factory Pattern, Service-Oriented Architecture
- **Key Modules**: Authentication, Educational Content Generation, Data Management, Real-time Communication
- **AI Integration**: OpenAI, Google AI, Vector Databases (Qdrant, Pinecone)

## 2. TASK PARSING CONFIGURATION

### TaskMaster Settings
```json
{
  "parsing": {
    "numTasks": "15-25",
    "complexity_threshold": 5,
    "research_enabled": true,
    "force_overwrite": false
  },
  "task_categories": [
    "Authentication & Authorization",
    "Educational Content Generation",
    "AI Integration & Prompt Engineering",
    "Data Management & Persistence",
    "Real-time Communication",
    "Background Processing & Queues",
    "API Development & Validation",
    "Testing & Quality Assurance",
    "Performance & Caching",
    "Security & Compliance"
  ]
}
```

### Parsing Rules
- **Module-based Task Generation**: Align tasks with NestJS module structure
- **Pattern Compliance**: Ensure tasks follow Strategy/Factory patterns
- **Educational Context**: Maintain focus on curriculum-aligned content generation
- **Technical Debt**: Include refactoring and optimization tasks

## 3. TASK STRUCTURE TEMPLATE

### Standard Task Format
```markdown
### Task ID: [Auto-generated]

**Title**: [Action] [Component/Feature] - [Brief Description]

**Description**: 
[Detailed explanation including educational context, technical requirements, and expected outcomes]

**Module/Service**: [Relevant NestJS module or service]

**Pattern**: [Strategy/Factory/Service pattern to follow]

**Priority**: [High/Medium/Low] based on:
- Educational impact
- Technical dependencies
- User experience impact
- System stability

**Complexity**: [1-10 scale] considering:
- Code complexity
- Integration requirements
- Testing needs
- Documentation requirements

**Dependencies**: [List of prerequisite task IDs]

**Acceptance Criteria**:
- [ ] Functional requirement 1
- [ ] Technical requirement 2
- [ ] Testing requirement 3
- [ ] Documentation requirement 4

**Technical Specifications**:
- **Files to Modify**: List of specific files
- **New Files**: Files to create
- **Database Changes**: Schema modifications if any
- **API Changes**: Endpoint modifications
- **Testing Requirements**: Unit/Integration/E2E tests

**Educational Context**:
- **Subject Areas**: Mathematics, Science, Language Arts, etc.
- **Grade Levels**: Primary 1-6, Secondary 1-4
- **Question Types**: multiple_choice, fill_blank, creative_writing, etc.
- **Cognitive Levels**: Knowledge, Understanding, Application, Analysis

**Implementation Notes**:
- Follow development-rule-be.md standards
- Use appropriate design patterns
- Implement proper error handling
- Include comprehensive logging

**Definition of Done**:
- [ ] Code implemented following NestJS patterns
- [ ] Unit tests written and passing
- [ ] Integration tests implemented
- [ ] API documentation updated
- [ ] Code review completed
- [ ] Educational content validation passed
```

## 4. PARSING WORKFLOW STEPS

### Step 1: Pre-parsing Analysis
1. **Review PRD Structure**: Analyze sections and feature priorities
2. **Codebase Assessment**: Understand current module structure
3. **Memory Bank Integration**: Incorporate existing project knowledge
4. **Dependency Mapping**: Identify technical and business dependencies

### Step 2: Task Generation Process
```bash
# Execute task parsing with research capabilities
npm run taskmaster parse-prd \
  --input .taskmaster/docs/prd.txt \
  --output .taskmaster/tasks/tasks.json \
  --numTasks 20 \
  --research true \
  --force false
```

### Step 3: Post-parsing Refinement
1. **Task Categorization**: Group by module and functionality
2. **Dependency Validation**: Ensure logical task ordering
3. **Complexity Assessment**: Run complexity analysis
4. **Priority Adjustment**: Align with educational and business priorities

### Step 4: Task Enhancement
1. **Technical Specification**: Add implementation details
2. **Educational Context**: Include curriculum alignment
3. **Testing Strategy**: Define comprehensive test requirements
4. **Documentation Requirements**: Specify documentation needs

## 5. EDUSG-SPECIFIC PARSING RULES

### Educational Content Tasks
- **Question Generation**: Tasks must specify question types and cognitive levels
- **Subject Hierarchy**: Follow topic → parentSubject → subject structure
- **Curriculum Alignment**: Include Singapore education standards
- **Grade Appropriateness**: Specify target grade levels

### Technical Implementation Tasks
- **NestJS Patterns**: Enforce modular architecture and dependency injection
- **Strategy Pattern**: Implement for topic-specific and exercise-type handling
- **Factory Pattern**: Use for creating appropriate strategies
- **Service Layer**: Encapsulate all business logic in services

### AI Integration Tasks
- **Prompt Engineering**: Include context-aware prompt construction
- **Multi-provider Support**: Implement fallback strategies
- **Vector Search**: Optimize embedding and search performance
- **Content Validation**: Ensure educational appropriateness

### Data Management Tasks
- **Multi-database**: Handle PostgreSQL and MongoDB integration
- **Caching Strategy**: Implement Redis-based caching
- **Migration Support**: Include database schema changes
- **Performance Optimization**: Focus on query optimization

## 6. TASK CATEGORIZATION MATRIX

| Category | Priority Weight | Complexity Range | Dependencies |
|----------|----------------|------------------|-------------|
| Core Educational Features | High | 6-9 | Authentication, Data Models |
| AI Integration | High | 7-10 | Core Features, Vector DB |
| Real-time Features | Medium | 5-8 | WebSocket, Authentication |
| Background Processing | Medium | 4-7 | Queue Setup, Core Features |
| Performance Optimization | Low-Medium | 3-6 | Core Implementation |
| Testing & Documentation | Medium | 2-5 | Feature Implementation |

## 7. QUALITY ASSURANCE CHECKLIST

### Task Validation Criteria
- [ ] **Educational Relevance**: Task contributes to learning outcomes
- [ ] **Technical Feasibility**: Implementation is achievable with current stack
- [ ] **Pattern Compliance**: Follows established architectural patterns
- [ ] **Dependency Logic**: Prerequisites are clearly defined
- [ ] **Acceptance Criteria**: Measurable and testable outcomes
- [ ] **Documentation**: Sufficient detail for implementation

### Code Quality Requirements
- [ ] **TypeScript Compliance**: Strict typing and interfaces
- [ ] **NestJS Standards**: Proper use of decorators and DI
- [ ] **Error Handling**: Comprehensive exception management
- [ ] **Testing Coverage**: Unit, integration, and E2E tests
- [ ] **Security Considerations**: Authentication and authorization

## 8. CONTINUOUS IMPROVEMENT

### Feedback Loop
1. **Task Completion Analysis**: Review actual vs. estimated effort
2. **Pattern Effectiveness**: Assess architectural pattern usage
3. **Educational Impact**: Measure learning outcome improvements
4. **Technical Debt**: Identify and address accumulating issues

### Workflow Updates
- **Monthly Review**: Update parsing rules based on project evolution
- **Pattern Refinement**: Improve architectural pattern implementation
- **Tool Enhancement**: Optimize TaskMaster configuration
- **Documentation Maintenance**: Keep workflow documentation current

## 9. INTEGRATION WITH DEVELOPMENT WORKFLOW

### Git Integration
```bash
# Create feature branch for task
git checkout -b feature/task-[ID]-[brief-description]

# Commit with task reference
git commit -m "feat: implement [feature] - closes task #[ID]"
```

### Code Review Process
- **Task Alignment**: Verify implementation matches task requirements
- **Pattern Compliance**: Ensure architectural patterns are followed
- **Educational Quality**: Validate content generation quality
- **Performance Impact**: Assess system performance implications

### Deployment Considerations
- **Database Migrations**: Include schema changes in deployment
- **Environment Variables**: Update configuration as needed
- **Cache Invalidation**: Handle cache updates for new features
- **Monitoring**: Add metrics for new functionality

## 10. TROUBLESHOOTING COMMON ISSUES

### Parsing Problems
- **Insufficient Context**: Enhance PRD with technical details
- **Dependency Conflicts**: Review and adjust task ordering
- **Complexity Underestimation**: Use historical data for calibration
- **Missing Educational Context**: Include curriculum alignment details

### Implementation Challenges
- **Pattern Misalignment**: Refer to development-rule-be.md
- **Integration Issues**: Check module dependencies and imports
- **Performance Problems**: Implement caching and optimization
- **Testing Difficulties**: Use proper mocking and test data

This workflow ensures consistent, high-quality task generation that aligns with the EduSG platform's educational mission and technical excellence standards.