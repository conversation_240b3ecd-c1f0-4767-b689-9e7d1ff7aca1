# Task ID: 13
# Title: Set up BullMQ for Background Processing
# Status: done
# Dependencies: 1, 2
# Priority: medium
# Description: Set up BullMQ for background job processing.
# Details:
Install BullMQ 5.49.2 (`bullmq`). Install Redis client (`ioredis` recommended). Configure a Redis connection for BullMQ (can reuse the connection from Task 2 if applicable, or set up a dedicated one). Create a basic BullMQ queue (e.g., `worksheetGenerationQueue`). Implement a simple job producer to add jobs to the queue and a basic worker to process them. Integrate this with NestJS using `@nestjs/bullmq`.

# Test Strategy:
Write integration tests to verify that jobs can be successfully added to the BullMQ queue and that a basic worker can pick up and process a job. Monitor Redis to confirm queue state changes.
