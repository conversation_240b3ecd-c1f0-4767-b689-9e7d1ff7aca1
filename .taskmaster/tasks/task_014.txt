# Task ID: 14
# Title: Implement Real-time Collaboration Infrastructure (Socket.IO)
# Status: done
# Dependencies: 1
# Priority: medium
# Description: Implement the basic infrastructure for real-time collaboration using Socket.IO.
# Details:
Install Socket.IO 4.8.1 (`socket.io`, `@nestjs/websockets`, `@nestjs/platform-socket.io`). Create a NestJS WebSocket Gateway (e.g., `CollaborationGateway`). Configure the gateway to listen for connections and basic events (e.g., `joinRoom`, `sendMessage`). Implement basic room management logic. This sets up the foundation for real-time features.

# Test Strategy:
Write integration tests using a Socket.IO client library in the test suite. Connect to the WebSocket gateway, join a room, send a message, and verify that the message is received by other clients in the same room (simulated in the test).
