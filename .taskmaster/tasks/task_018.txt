# Task ID: 18
# Title: Implement Distribution Enforcement and Balancing in Question Pool Selection
# Status: done
# Dependencies: 5, 9, 16
# Priority: medium
# Description: Implement algorithms to enforce specified distributions of difficulty levels (e.g., 20% Easy, 60% Medium, 20% Advanced) and balance question types during random question selection from the pool. This includes weighted selection, diversity mechanisms, and a quality validation pipeline.
# Details:
This task involves enhancing the `QuestionPoolService`, specifically the `getRandomQuestions` method (developed in Task 16), to implement sophisticated distribution enforcement and balancing algorithms.
Key implementation aspects:
1.  **Difficulty Level Distribution:** Modify `getRandomQuestions` to accept or retrieve configuration for target difficulty level distributions (e.g., 20% Easy, 60% Medium, 20% Advanced). Implement logic within the MongoDB aggregation pipeline or in post-processing to select questions that meet these distribution targets. The `Question` model (Task 5) must have a `difficultyLevel` field (e.g., 'Easy', 'Medium', 'Advanced').
2.  **Question Type Balancing:** Implement mechanisms to ensure a balanced mix of question types based on configurable parameters or heuristics. The `Question` model (Task 5) must have a `questionType` field.
3.  **Weighted Selection Mechanisms:** Introduce weighting factors in the selection process, potentially based on achieving desired distributions, question quality, or recency.
4.  **Diversity Algorithms:** Implement strategies to prevent excessive repetition of questions. Consider adding fields like `lastSelectedTimestamp` or `selectionFrequency` to the `Question` model (ensure Task 5 accounts for this or update as part of this task). The selection algorithm should penalize or deprioritize recently or frequently selected questions.
5.  **Quality Validation Pipeline Integration:** After initial selection, pass questions through a quality validation pipeline utilizing the `ContentValidationService` (from Task 9) for educational appropriateness checks (as per Phase 3 specifications). Questions failing validation should be handled according to defined rules.
6.  **Configuration:** Define how distribution rules, balancing parameters, and diversity settings will be configured (e.g., global settings, per-request parameters).
7.  **Fallback Strategies:** Define behavior if the pool cannot satisfy requested distributions or diversity criteria (e.g., best effort, logging, error handling).

# Test Strategy:
1.  **Unit Tests:** Test `DistributionLogic` for difficulty level percentages, `QuestionTypeBalancingLogic`, `WeightedSelection` for correct prioritization, and `DiversityAlgorithm` for minimized repetition. Mock `ContentValidationService` to test `QualityValidationIntegration`.
2.  **Integration Tests (within `QuestionPoolService`):** Test `getRandomQuestions` with a seeded MongoDB test database. Verify adherence to distribution/balancing parameters (including the new difficulty level distributions), diversity rule enforcement, and actual `ContentValidationService` integration.
3.  **Scenario Tests:** Simulate generating multiple worksheets to assess overall distribution (including difficulty levels) and diversity. Test fallback strategies with insufficient or skewed pool data.
4.  **Configuration Testing:** Verify that changes in configuration for distributions (including difficulty levels), balancing, and diversity are correctly applied by the selection algorithms.

# Subtasks:
## 1. Implement Core Difficulty Distribution Logic and Configuration in `getRandomQuestions` [done]
### Dependencies: None
### Description: Modify `QuestionPoolService.getRandomQuestions` to support configurable difficulty level distributions (e.g., 20% Easy, 60% Medium, 20% Advanced). This includes defining the configuration mechanism and implementing the initial selection logic to meet these targets, ensuring the `Question` model has a `difficultyLevel` field.
### Details:
Define a configuration schema for difficulty distributions (e.g., `{"Easy": 0.2, "Medium": 0.6, "Advanced": 0.2}`). Decide if this is a global setting, per-pool setting, or a parameter to `getRandomQuestions`. Update `getRandomQuestions` method signature if necessary. Implement selection logic using MongoDB aggregation (`$facet`), multiple queries, or post-processing to select questions per difficulty level. Verify `Question` model (Task 5) includes `difficultyLevel` (e.g., 'Easy', 'Medium', 'Advanced').

## 2. Integrate Question Type Balancing and Weighted Selection Framework [done]
### Dependencies: 18.1
### Description: Extend the question selection process in `getRandomQuestions` to ensure a balanced mix of question types based on configurable parameters. Introduce a foundational weighted selection mechanism that can be used to influence choices based on various criteria. Ensure the `Question` model has a `questionType` field.
### Details:
Define configuration for question type balancing (e.g., target percentages per type, or a 'prefer diversity' flag). Modify `getRandomQuestions` to incorporate type balancing, potentially by adjusting counts for difficulty+type combinations or post-processing the difficulty-selected set. Implement a generic weighting system (e.g., adding a temporary `selectionScore` to candidate questions) that can initially help achieve difficulty/type targets. Verify `Question` model (Task 5) includes `questionType`.

## 3. Implement Diversity Algorithms using Timestamps and Frequency [done]
### Dependencies: 18.2
### Description: Enhance the selection algorithm in `getRandomQuestions` to promote diversity and prevent excessive repetition of questions. This involves adding tracking fields to the `Question` model and using them to penalize recently or frequently selected questions, potentially leveraging the weighted selection framework.
### Details:
Add `lastSelectedTimestamp` (Date) and `selectionFrequency` (Number) fields to the `Question` model (if not already present from Task 5, consider this an update). Ensure these fields are updated after a question is selected. Modify `getRandomQuestions` to fetch these fields and use the weighting mechanism (from subtask 2) to assign lower scores/weights to questions selected recently or with high frequency. Define configurable parameters for how recency and frequency affect selection.

## 4. Integrate Quality Validation Pipeline using `ContentValidationService` [done]
### Dependencies: 18.3
### Description: After an initial set of questions is selected based on distribution, type, and diversity criteria, pass them through a quality validation pipeline using the `ContentValidationService` (from Task 9) for educational appropriateness checks. Implement logic for handling questions that fail validation.
### Details:
In `QuestionPoolService.getRandomQuestions`, after selection logic (difficulty, type, diversity) is complete, call `ContentValidationService.validateQuestions(selectedQuestions)`. Define and implement rules for handling questions that fail validation (e.g., discard and attempt to fetch replacements respecting original criteria, discard and return a smaller set, or log failures and proceed). Make this handling rule configurable or based on predefined policy.

## 5. Implement Fallback Strategies and Finalize Comprehensive Configuration [done]
### Dependencies: 18.1, 18.2, 18.3, 18.4
### Description: Define and implement robust fallback strategies within `getRandomQuestions` for scenarios where the question pool cannot satisfy all requested distributions, balancing, diversity, or quality criteria. Consolidate and finalize the configuration mechanisms for all selection parameters.
### Details:
Implement fallback logic: if exact criteria (difficulty, type, diversity) cannot be met, allow 'best effort' selection with logging. If not enough questions pass quality validation and replacements are not found, return fewer questions or throw an error based on configuration. Consolidate all configuration settings (difficulty distribution, type balancing, diversity parameters, validation failure handling, fallback behavior) into a clear structure. Ensure comprehensive logging for selection decisions, applied configurations, and triggered fallbacks.

