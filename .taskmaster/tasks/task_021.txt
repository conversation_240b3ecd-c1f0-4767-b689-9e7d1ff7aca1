# Task ID: 21
# Title: Implement Comprehensive Error Handling for Random Question Pool Selection
# Status: pending
# Dependencies: 4, 14, 16, 17, 19, 20
# Priority: medium
# Description: Implement robust error handling and recovery mechanisms for the random question pool selection process, covering database connectivity, insufficient content, AI service failures, and WebSocket communication.
# Details:
1. **MongoDB Connection Retry Logic:** Modify `QuestionPoolService` or the underlying data access layer to implement retry logic for MongoDB operations. Configure for 3 retry attempts with exponential backoff upon connection failures or transient errors during question retrieval. Log each attempt and the final status.
2. **Insufficient Questions Graceful Degradation & AI Fallback:** Enhance the question sourcing logic (likely in `WorksheetGenerateConsumer` or an orchestrator service) to handle scenarios where the question pool (Task 16) returns fewer questions than requested. If a deficit occurs, trigger AI question generation (using services from Task 4, potentially orchestrated by logic from Task 17) to fulfill the remaining count. This fallback should be configurable (Task 20).
3. **AI Service Failure Chain:** Implement a resilient AI interaction layer (e.g., a new `AiOrchestrationService`). This service will attempt to generate questions sequentially: first from OpenAI, then upon failure, from Google AI. If both AI services fail, it will attempt to retrieve questions from a 'Cached Content' source as a final fallback. Log the outcome of each attempt and the service ultimately used.
4. **WebSocket Error Communication:** Integrate with the WebSocket gateway (Task 14) to communicate errors effectively to the client. Define specific error event types (e.g., `worksheet:generation:error`, `database:unavailable`, `ai:service:failed`, `insufficient_questions:no_fallback`) and structured error payloads (error code, message, details). Ensure errors from all parts of the question selection and generation pipeline are caught and propagated correctly.
5. **Comprehensive Logging:** Implement detailed logging using NestJS `LoggerService` for all error events, retry attempts, fallback invocations, and AI service interactions to facilitate debugging and monitoring.

# Test Strategy:
1. **MongoDB Connection Failure:** Simulate MongoDB unavailability (e.g., stop service, invalid connection string). Verify that the system attempts reconnection 3 times with backoff (check logs) and eventually communicates a DB error via WebSocket if unsuccessful.
2. **Insufficient Questions & AI Fallback:** Configure the question pool with fewer questions than a test request. Verify the system identifies the deficit, logs it, and successfully triggers the AI fallback to generate the missing questions. Mock AI services to confirm they are called with correct parameters. The final question set should meet the requested count.
3. **AI Service Failure Chain:** 
    a. Simulate OpenAI API failure (e.g., mock endpoint to return error, invalid API key). Verify the system logs the failure and automatically attempts to use Google AI. 
    b. Simulate both OpenAI and Google AI failures. Verify the system attempts to use 'Cached Content'. 
    c. Simulate failure of all three sources. Verify a specific error is logged and communicated via WebSocket.
4. **WebSocket Error Communication:** For each error scenario (DB down, all AI services down, insufficient questions with no successful fallback), use a WebSocket client to verify that the correct, structured error messages/events are received as defined in the error handling protocol.
5. **System Stability:** Induce various combinations of failures to ensure no unhandled exceptions crash the application. Verify that logs provide clear and actionable information for each error scenario.

# Subtasks:
## 1. Implement MongoDB Connection Retry Logic in QuestionPoolService [pending]
### Dependencies: None
### Description: Modify `QuestionPoolService` or the underlying data access layer to implement retry logic for MongoDB operations. This will enhance resilience against transient database connectivity issues when retrieving questions.
### Details:
Configure the retry mechanism for 3 retry attempts with exponential backoff (e.g., initial delay 1s, then 2s, then 4s) upon connection failures or transient errors (e.g., network timeouts, temporary unavailability) during question retrieval operations. Log each retry attempt, including the attempt number and delay, and the final status (success or failure after all retries) using NestJS `LoggerService`. Consider using a library like `async-retry` or implementing a custom retry decorator within the service or data access layer.

## 2. Implement Graceful Degradation for Insufficient Questions with AI Fallback [pending]
### Dependencies: 21.1
### Description: Enhance the question sourcing logic, likely within `WorksheetGenerateConsumer` or an orchestrator service, to gracefully handle scenarios where the primary question pool (Task 16, accessed via `QuestionPoolService`) returns fewer questions than requested by the user or system. If a deficit occurs, trigger AI question generation to fulfill the remaining count.
### Details:
After attempting to retrieve questions from MongoDB (with retries from subtask 1), check if the number of questions obtained is less than the required number. If there's a deficit, calculate the number of missing questions. Invoke an AI question generation service (which will be the `AiOrchestrationService` from subtask 3) to generate the exact number of missing questions. This AI fallback mechanism should be configurable (as per Task 20), allowing it to be enabled or disabled. Log the detected deficit, the number of questions requested versus found, and the activation of the AI fallback using NestJS `LoggerService`.

## 3. Implement Resilient AI Service Failure Chain in AiOrchestrationService [pending]
### Dependencies: 21.2
### Description: Develop or enhance an `AiOrchestrationService` to manage sequential attempts for question generation across multiple AI providers (OpenAI, then Google AI) and a final 'Cached Content' source as a fallback. This service will be invoked when the AI fallback is triggered (from subtask 2).
### Details:
The `AiOrchestrationService` will implement a chain of responsibility or sequential call pattern: 1. Attempt to generate questions using the primary AI provider (e.g., OpenAI, using services from Task 4). 2. If the primary AI provider fails (e.g., API error, timeout, malformed response), attempt generation using a secondary AI provider (e.g., Google AI). 3. If all configured AI providers fail, attempt to retrieve suitable questions from a 'Cached Content' source (e.g., a pre-populated, curated list of questions in a database or file store). Log the outcome of each attempt in the chain (provider name, success/failure, error details if any) and which service (OpenAI, Google AI, Cached Content, or none) ultimately fulfilled the request or if all failed. This service should be designed to be extensible for future AI providers.

## 4. Integrate WebSocket Error Communication for Question Generation Failures [pending]
### Dependencies: 21.1, 21.2, 21.3
### Description: Integrate with the WebSocket gateway (from Task 14) to communicate errors encountered during the random question pool selection and AI generation pipeline effectively to the connected client. This ensures the client is informed of issues in real-time.
### Details:
Define specific error event types for WebSocket communication, such as `worksheet:generation:error`. Structure error payloads consistently, including: `errorCode` (e.g., `DATABASE_UNAVAILABLE`, `AI_SERVICE_FAILED`, `INSUFFICIENT_QUESTIONS_NO_FALLBACK`, `MAX_RETRIES_REACHED`), `message` (a user-friendly explanation), and optionally `details` (contextual information, non-sensitive). Ensure that errors originating from `QuestionPoolService` (subtask 1), insufficient question handling in `WorksheetGenerateConsumer` (subtask 2), and `AiOrchestrationService` (subtask 3) are caught and mapped to these structured WebSocket error events. For instance, if all fallbacks in `AiOrchestrationService` fail, a specific `INSUFFICIENT_QUESTIONS_NO_FALLBACK` error should be sent.

## 5. Implement Comprehensive Logging for All Error Handling Mechanisms [pending]
### Dependencies: 21.1, 21.2, 21.3, 21.4
### Description: Implement detailed and structured logging using NestJS `LoggerService` across all newly implemented error handling components (MongoDB retries, insufficient content fallbacks, AI service chain, WebSocket error reporting) to facilitate robust debugging, monitoring, and operational auditing.
### Details:
Ensure comprehensive logging is added for: 
1. MongoDB retries (Subtask 1): Log each retry attempt, delay duration, specific error triggering retry, and final outcome (success or failure after all retries).
2. Insufficient questions & AI fallback (Subtask 2): Log when a deficit is detected, the number of questions requested vs. found, and when the AI fallback mechanism is activated or skipped due to configuration.
3. AI service chain (Subtask 3): Log each attempt to an AI provider (OpenAI, Google AI) or Cached Content, including parameters sent (if not sensitive), success/failure status, error messages from providers, and which provider ultimately succeeded or if all failed.
4. WebSocket error communication (Subtask 4): Log the error event type and payload being sent to the client via WebSockets.
5. General error conditions: Log any unexpected exceptions caught within the error handling logic itself, providing stack traces and context (e.g., worksheet ID, user ID if available). Use structured logging (JSON format if possible) for easier parsing and analysis by log management systems.

