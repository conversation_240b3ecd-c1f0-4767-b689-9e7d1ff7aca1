# Task ID: 6
# Title: Develop Prompt Engineering System
# Status: done
# Dependencies: 4, 5
# Priority: high
# Description: Develop a system for building dynamic and context-aware prompts for AI content generation.
# Details:
Create a `PromptService` that takes parameters like subject, topic, cognitive level, question type, difficulty, and curriculum context (retrieved from DB via models defined in Task 5). Implement logic to construct well-structured prompts for the AI services (OpenAI, Google AI) using prompt templates. Research and apply best practices for educational content prompt engineering (e.g., specifying output format, tone, target age group).

# Test Strategy:
Write unit tests for the `PromptService` to verify that prompts are correctly generated based on various input parameters and combinations. Test edge cases and different content types.
