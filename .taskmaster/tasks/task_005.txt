# Task ID: 5
# Title: Define Educational Content Data Models
# Status: done
# Dependencies: 2
# Priority: high
# Description: Define Mongoose schemas and models for the educational content hierarchy (Subject, Topic, Question, etc.).
# Details:
Define Mongoose schemas for `Subject`, `Topic`, `Question`, `Worksheet`, etc. Model the hierarchy: `Topic` belongs to `Subject`, `Subject` can have a `parentSubject`. Include fields for question types, difficulty levels, cognitive complexity, curriculum alignment metadata. Establish relationships between models (e.g., `Worksheet` contains `Question` references). Use Mongoose features like population for querying related data.

# Test Strategy:
Write unit tests for each schema to ensure correct field types, required fields, and relationships are defined. Use Mongoose model methods in tests to create and query documents, verifying data integrity and relationships.
