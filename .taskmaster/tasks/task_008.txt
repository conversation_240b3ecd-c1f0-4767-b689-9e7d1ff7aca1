# Task ID: 8
# Title: Build Question Type Factories
# Status: done
# Dependencies: 6, 7
# Priority: high
# Description: Create factories to select and instantiate the correct question generation strategy based on input.
# Details:
Create a `QuestionStrategyFactory` service that takes the desired question type as input and returns the appropriate strategy implementation (e.g., an instance of `MultipleChoiceStrategy`). This factory will use the prompt generated by the `PromptService` (Task 6) and the strategies defined in Task 7. Inject necessary dependencies (like the `AiService`) into the strategies via the factory or dependency injection.

# Test Strategy:
Write unit tests for the `QuestionStrategyFactory` to verify that it returns the correct strategy instance for each supported question type input. Test handling of unsupported types.
