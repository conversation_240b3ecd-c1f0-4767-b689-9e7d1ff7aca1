# Task ID: 15
# Title: Develop Basic Question Pool Management
# Status: done
# Dependencies: 5
# Priority: medium
# Description: Develop the basic system for storing, retrieving, and managing questions in a pool.
# Details:
Use the `Question` model defined in Task 5. Create a `QuestionPoolService` and API endpoints (e.g., GET /questions, POST /questions, GET /questions/:id). Implement basic CRUD operations for questions in the pool. Include fields for metadata relevant to the pool (e.g., source, quality score). Implement a basic retrieval method to fetch questions based on criteria (subject, topic, type).

# Test Strategy:
Write integration tests using Supertest for the question pool API endpoints. Test creating, retrieving (single and list), updating, and deleting questions. Verify data persistence and retrieval accuracy.
