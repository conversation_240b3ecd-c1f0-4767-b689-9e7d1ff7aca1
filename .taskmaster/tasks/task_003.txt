# Task ID: 3
# Title: Implement Basic User Authentication and Authorization
# Status: done
# Dependencies: 1, 2
# Priority: high
# Description: Implement basic user authentication and authorization using JWT.
# Details:
Install authentication dependencies: `@nestjs/passport`, `passport`, `passport-jwt`, `@nestjs/jwt`, `jsonwebtoken`. Implement a `AuthModule` with `JwtStrategy`. Create endpoints for user registration and login. Generate and return JWT tokens upon successful authentication. Implement basic guards (`@nestjs/common` `AuthGuard`) to protect routes. Define basic user roles (e.g., Teacher, Admin) and implement role-based authorization using `@nestjs/common` `CanActivate` or a custom decorator.

# Test Strategy:
Write integration tests using Supertest to verify user registration, login with correct/incorrect credentials, and access to protected routes with/without a valid JWT.
