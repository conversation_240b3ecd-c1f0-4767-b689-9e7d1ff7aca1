# Task ID: 4
# Title: Integrate Basic AI Services (OpenAI, Google AI)
# Status: done
# Dependencies: 1
# Priority: high
# Description: Integrate OpenAI and Google Generative AI services and create a service to handle API calls.
# Details:
Install AI SDKs: `openai` 4.94.0, `@google/generative-ai` 0.24.0. Create an `AiModule` and an `AiService`. Configure API keys securely (e.g., using `ConfigModule`). Implement methods in `AiService` to make basic text generation calls to both OpenAI and Google AI APIs. Implement basic error handling and logging for API calls. Consider a simple factory or strategy to switch between providers.

# Test Strategy:
Write unit tests for the `AiService` methods, mocking the external API calls to ensure correct request parameters are formed and responses are handled. Write integration tests to make actual calls to verify connectivity and basic functionality (using test API keys if available).
