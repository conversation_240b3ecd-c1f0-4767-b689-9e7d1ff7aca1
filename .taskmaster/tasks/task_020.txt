# Task ID: 20
# Title: Implement Configuration Management for Random Question Pool Selection
# Status: done
# Dependencies: 1, 15
# Priority: medium
# Description: Implement a configuration management system for the random question pool selection feature. This includes managing environment variables, feature flags for different selection strategies, and defining a `WorksheetGenerationOptions` interface with validation and default values.
# Details:
1. **Setup Configuration Module**:
    *   Utilize NestJS's `@nestjs/config` module.
    *   Create a typed configuration file (e.g., `question-pool.config.ts`) using `registerAs` to define and export the configuration schema. Load this using `ConfigModule.forRoot({ load: [questionPoolConfig] })`.
    *   Ensure this configuration is globally available or imported into relevant modules.
2. **Environment Variables & Typed Configuration**:
    *   Define and manage the following settings, loadable from environment variables, with clear defaults in `question-pool.config.ts`:
        *   `QUESTION_POOL_ENABLED`: boolean (default: `true`). Controls overall usage of the question pool.
        *   `DEFAULT_SELECTION_STRATEGY`: string enum (`'pool-only'`, `'ai-only'`, `'hybrid'`, `'mixed'`; default: `'hybrid'`). Specifies the default strategy for question sourcing.
        *   `MIN_POOL_QUESTIONS_THRESHOLD`: number (default: `10`). Minimum number of questions required in the pool for it to be considered a primary source under certain strategies.
3. **Feature Flags for Selection Strategies (within Config)**:
    *   Extend the typed configuration to manage feature flags for selection strategies:
        *   `allow_pool_only_strategy`: boolean (default: `true`)
        *   `allow_ai_only_strategy`: boolean (default: `true`)
        *   `allow_hybrid_strategy`: boolean (default: `true`)
        *   `allow_mixed_strategy`: boolean (default: `true`)
    *   These flags will determine which strategies are permissible for selection when processing `WorksheetGenerationOptions`.
4. **`WorksheetGenerationOptions` DTO Definition**:
    *   Define a Data Transfer Object (DTO) class, `WorksheetGenerationOptionsDto`, for options related to worksheet generation, particularly those affecting question sourcing. This DTO will be used for input validation in controllers or message handlers.
    *   Properties to include (all optional, with defaults applied by consuming services based on global config):
        *   `selectionStrategy?: string` (e.g., `'pool-only'`, `'ai-only'`, `'hybrid'`, `'mixed'`)
        *   `useQuestionPoolOverride?: boolean`
        *   `minPoolQuestionsRequired?: number`
    *   **Validation**: Use `class-validator` decorators within the DTO for robust validation:
        *   `@IsOptional() @IsEnum(['pool-only', 'ai-only', 'hybrid', 'mixed']) selectionStrategy?: string;` (further validation against active feature flags will be done in service layer).
        *   `@IsOptional() @IsBoolean() useQuestionPoolOverride?: boolean;`
        *   `@IsOptional() @IsInt() @Min(0) minPoolQuestionsRequired?: number;`
5. **Configuration Service/Injection**:
    *   Ensure the typed configuration (e.g., `QuestionPoolConfigType` from `question-pool.config.ts`) is injectable into services like `QuestionPoolService`, `WorksheetGenerateConsumer`, etc., using `@Inject(questionPoolConfig.KEY)`.
    *   Consuming services will use these global configurations as defaults and then apply overrides from `WorksheetGenerationOptionsDto` if provided and valid.

# Test Strategy:
1. **Unit Tests for Configuration Loading (`question-pool.config.ts`)**:
    *   Verify correct loading of environment variables (e.g., using `process.env` mocks).
    *   Test that default values are correctly applied when specific environment variables are not set.
    *   Test that environment variables correctly override the default values for `QUESTION_POOL_ENABLED`, `DEFAULT_SELECTION_STRATEGY`, and `MIN_POOL_QUESTIONS_THRESHOLD`.
2. **Unit Tests for Feature Flag Logic (within Config or Consuming Service)**:
    *   Verify that feature flags for selection strategies (`allow_pool_only_strategy`, etc.) are correctly read from the configuration.
    *   Test logic that checks if a requested strategy in `WorksheetGenerationOptionsDto` is permitted based on these flags.
3. **Unit Tests for `WorksheetGenerationOptionsDto` Validation**:
    *   Use `class-validator`'s `validate` function to test the DTO.
    *   Test with valid inputs for all properties.
    *   Test with invalid inputs: incorrect enum for `selectionStrategy`, non-boolean for `useQuestionPoolOverride`, non-integer or negative for `minPoolQuestionsRequired`.
    *   Verify that validation errors are correctly reported.
4. **Integration Tests for Config Injection and Usage**:
    *   Create a test NestJS module that registers the `ConfigModule` with `question-pool.config.ts`.
    *   Inject the typed configuration into a simple test service.
    *   Assert that the test service receives the correct configuration values based on mock environment settings.
5. **Service-Level Tests (Focus on Config Consumption)**:
    *   For services like `WorksheetGenerateConsumer` or `QuestionPoolService` (or simplified versions for testing this aspect):
        *   Verify they correctly access and utilize `QUESTION_POOL_ENABLED`.
        *   Verify they use `DEFAULT_SELECTION_STRATEGY` when `WorksheetGenerationOptionsDto.selectionStrategy` is undefined.
        *   Verify they prioritize `WorksheetGenerationOptionsDto.selectionStrategy` if provided and allowed by feature flags.
        *   Test the behavior when `WorksheetGenerationOptionsDto.selectionStrategy` is provided but not allowed by feature flags (e.g., error, fallback to default).

# Subtasks:
## 1. Setup NestJS Configuration Module and Initial `question-pool.config.ts` [done]
### Dependencies: None
### Description: Initialize and configure the `@nestjs/config` module. Create the `question-pool.config.ts` file using `registerAs` to define the basic structure for question pool related configurations, and ensure it's loaded globally.
### Details:
1. Install `@nestjs/config` package if not already present.
2. Create a new configuration file, e.g., `src/config/question-pool.config.ts`.
3. Inside this file, use `registerAs` from `@nestjs/config` to define a configuration namespace, e.g., `questionPoolConfig = registerAs('questionPool', () => ({}))`. This will be populated in subsequent tasks.
4. In the main application module (e.g., `app.module.ts`) or a relevant feature module, import `ConfigModule` from `@nestjs/config` and `questionPoolConfig`.
5. Configure `ConfigModule` to load the custom configuration: `ConfigModule.forRoot({ load: [questionPoolConfig], isGlobal: true })`. Setting `isGlobal: true` makes the configuration available throughout the application without needing to import `ConfigModule` in every module.

## 2. Define Core Settings (Env Vars & Defaults) in `question-pool.config.ts` [done]
### Dependencies: 20.1
### Description: Define and integrate `QUESTION_POOL_ENABLED`, `DEFAULT_SELECTION_STRATEGY`, and `MIN_POOL_QUESTIONS_THRESHOLD` into `question-pool.config.ts`. Ensure these are loadable from environment variables with specified defaults and properly typed.
### Details:
1. Modify `src/config/question-pool.config.ts` to include the following properties within the `registerAs` callback:
   - `QUESTION_POOL_ENABLED`: Load from `process.env.QUESTION_POOL_ENABLED`. Parse as boolean. Default to `true`.
   - `DEFAULT_SELECTION_STRATEGY`: Load from `process.env.DEFAULT_SELECTION_STRATEGY`. String enum (`'pool-only'`, `'ai-only'`, `'hybrid'`, `'mixed'`). Default to `'hybrid'`.
   - `MIN_POOL_QUESTIONS_THRESHOLD`: Load from `process.env.MIN_POOL_QUESTIONS_THRESHOLD`. Parse as integer. Default to `10`.
2. Define a TypeScript interface or type (e.g., `QuestionPoolConfig`) representing the structure of this configuration object and use it with `registerAs` for type safety: `registerAs<QuestionPoolConfig>('questionPool', () => ({...}))`.
3. Implement parsing logic for environment variables (e.g., `parseInt` for numbers, string to boolean conversion for booleans) and ensure defaults are applied if environment variables are not set.

## 3. Add Selection Strategy Feature Flags to `question-pool.config.ts` [done]
### Dependencies: 20.2
### Description: Extend `question-pool.config.ts` to include boolean feature flags: `allow_pool_only_strategy`, `allow_ai_only_strategy`, `allow_hybrid_strategy`, `allow_mixed_strategy`. These should also be loadable from environment variables with defaults.
### Details:
1. Further modify `src/config/question-pool.config.ts` by adding the following properties to the object returned by the `registerAs` callback:
   - `allow_pool_only_strategy`: Load from `process.env.ALLOW_POOL_ONLY_STRATEGY`. Parse as boolean. Default to `true`.
   - `allow_ai_only_strategy`: Load from `process.env.ALLOW_AI_ONLY_STRATEGY`. Parse as boolean. Default to `true`.
   - `allow_hybrid_strategy`: Load from `process.env.ALLOW_HYBRID_STRATEGY`. Parse as boolean. Default to `true`.
   - `allow_mixed_strategy`: Load from `process.env.ALLOW_MIXED_STRATEGY`. Parse as boolean. Default to `true`.
2. Update the `QuestionPoolConfig` TypeScript interface/type defined in Subtask 2 to include these new feature flag properties.
3. Ensure robust parsing for these boolean flags from environment variables, similar to `QUESTION_POOL_ENABLED`.

## 4. Define `WorksheetGenerationOptionsDto` with `class-validator` Rules [done]
### Dependencies: None
### Description: Create the `WorksheetGenerationOptionsDto` class with optional properties: `selectionStrategy`, `useQuestionPoolOverride`, and `minPoolQuestionsRequired`. Implement robust input validation for these properties using `class-validator` decorators.
### Details:
1. Install `class-validator` and `class-transformer` packages if not already present.
2. Create a new DTO file, e.g., `src/dtos/worksheet-generation-options.dto.ts`.
3. Define the `WorksheetGenerationOptionsDto` class with the following optional properties and `class-validator` decorators:
   - `selectionStrategy?: string;`
     - Decorators: `@IsOptional()`, `@IsEnum(['pool-only', 'ai-only', 'hybrid', 'mixed'], { message: 'Invalid selection strategy. Must be one of: pool-only, ai-only, hybrid, mixed' })`
   - `useQuestionPoolOverride?: boolean;`
     - Decorators: `@IsOptional()`, `@IsBoolean()`
   - `minPoolQuestionsRequired?: number;`
     - Decorators: `@IsOptional()`, `@IsInt()`, `@Min(0)`
4. This DTO will be used in controllers or message handlers to validate incoming requests or messages related to worksheet generation options.

## 5. Implement Configuration Injection and Service-Level Override Logic [done]
### Dependencies: 20.2, 20.3, 20.4
### Description: Ensure the typed `QuestionPoolConfigType` (from `question-pool.config.ts`) is injectable into relevant services (e.g., `QuestionPoolService`, `WorksheetGenerateConsumer`). Implement logic within these services to use global configurations as defaults, apply valid overrides from `WorksheetGenerationOptionsDto`, and perform service-level validation such as checking `selectionStrategy` against active feature flags.
### Details:
1. In services that handle worksheet generation or question selection (e.g., `WorksheetService`, `QuestionPoolService`):
   - Inject the typed configuration using `@Inject(questionPoolConfig.KEY) private readonly config: QuestionPoolConfigType` (assuming `QuestionPoolConfigType` is the exported type of your `questionPoolConfig`).
2. Implement or refactor methods that determine the final worksheet generation settings. These methods will typically accept an optional `WorksheetGenerationOptionsDto`.
3. The logic should be as follows:
   a. Start with default values derived from the injected `this.config` (e.g., `effectiveStrategy = this.config.DEFAULT_SELECTION_STRATEGY`, `effectiveMinPoolQuestions = this.config.MIN_POOL_QUESTIONS_THRESHOLD`).
   b. If a `WorksheetGenerationOptionsDto` (let's call it `optionsDto`) is provided:
      i. If `optionsDto.selectionStrategy` is present, check if this strategy is allowed by the corresponding feature flag in `this.config` (e.g., if `optionsDto.selectionStrategy === 'pool-only'`, check `this.config.allow_pool_only_strategy`). If allowed, update `effectiveStrategy`. If not allowed, either fall back to `this.config.DEFAULT_SELECTION_STRATEGY` or handle as an error, based on requirements.
      ii. If `optionsDto.useQuestionPoolOverride` is present, its value should influence whether the question pool is used (this might interact with `QUESTION_POOL_ENABLED` and the chosen strategy).
      iii. If `optionsDto.minPoolQuestionsRequired` is present and valid (non-negative integer), update `effectiveMinPoolQuestions`.
4. Ensure that the overall `QUESTION_POOL_ENABLED` config flag is respected as a primary control.

