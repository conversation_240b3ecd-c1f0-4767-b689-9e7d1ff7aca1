# Task ID: 17
# Title: Enhance WorksheetGenerateConsumer with Hybrid Question Sourcing (Pool & AI)
# Status: pending
# Dependencies: 4, 8, 9, 13, 14, 16
# Priority: medium
# Description: Modify `WorksheetGenerateConsumer` to implement a hybrid question sourcing strategy, prioritizing questions from a predefined pool and using AI generation as a fallback. This includes configuration options for question source selection and maintaining existing progress tracking and WebSocket update functionalities.
# Details:
1. **Modify `WorksheetGenerateConsumer`**: Update the consumer logic to integrate with `QuestionPoolService` (Task 16) for fetching questions and with `AiService` (Task 4) and `QuestionStrategyFactory` (Task 8) for AI question generation.
2. **Implement Hybrid Strategy (Pool-First, AI Fallback)**: First, attempt to retrieve questions from the pool using `QuestionPoolService.getRandomQuestions`. If insufficient, calculate the deficit and trigger AI question generation for the remaining questions using appropriate strategies and AI services. Apply filters (subject, topic, type, difficulty, etc.) as specified.
3. **Configuration for Question Source**: Introduce a configuration parameter in the worksheet generation request or consumer settings to specify the question source: `POOL_ONLY`, `AI_ONLY`, `HYBRID_POOL_FIRST` (default). 
4. **Question Type Mapping and Validation**: Ensure seamless mapping between requested question types and those available/generated. Utilize `QuestionStrategyFactory` (Task 8) for AI generation of specific types. Integrate `ContentValidationService` (Task 9) to validate all questions.
5. **Maintain Existing Functionality**: Ensure existing progress tracking mechanisms and WebSocket updates (Task 14) remain functional.
6. **Error Handling and Logging**: Implement robust error handling for pool issues, AI API failures, or validation failures. Add detailed logging for the question sourcing process.

# Test Strategy:
1. **Unit Tests**: Test `WorksheetGenerateConsumer` logic for each source configuration (`POOL_ONLY`, `AI_ONLY`, `HYBRID_POOL_FIRST`). Mock dependencies (`QuestionPoolService`, AI services, `QuestionStrategyFactory`) to verify correct calls and logic for deficit calculation and AI fallback. Test type mapping and validation integration.
2. **Integration Tests**: Test end-to-end flow with `WorksheetGenerateConsumer` in BullMQ (Task 13), interacting with `QuestionPoolService` (Task 16) and `AiService` (Task 4). Test scenarios: pool has enough questions, pool has some questions (triggering AI fallback), pool has no relevant questions.
3. **Functional Tests**: Trigger worksheet generation jobs with different source configurations. Verify generated worksheets contain questions from expected sources, correct number/types of questions, and that content validation (Task 9) is applied. Verify progress tracking and WebSocket updates (Task 14) function correctly. Check logs for sourcing details.

# Subtasks:
## 1. Define and Integrate Question Source Configuration [pending]
### Dependencies: None
### Description: Modify `WorksheetGenerateConsumer` to accept and process a new `questionSourceStrategy` configuration parameter (`POOL_ONLY`, `AI_ONLY`, `HYBRID_POOL_FIRST` with `HYBRID_POOL_FIRST` as default). This sets the foundation for conditional question sourcing logic.
### Details:
Define an enum or constants for the `questionSourceStrategy` values: `POOL_ONLY`, `AI_ONLY`, `HYBRID_POOL_FIRST`. Update the worksheet generation request DTO or consumer's input parameters to include this new field. Implement logic within the consumer to read this configuration at the start of processing, defaulting to `HYBRID_POOL_FIRST` if not specified. Store the chosen strategy for use in subsequent steps.

## 2. Implement Question Sourcing from Question Pool [pending]
### Dependencies: 17.1
### Description: Integrate `QuestionPoolService` (Task 16) into `WorksheetGenerateConsumer`. Implement logic to fetch questions from the pool using specified filters (subject, topic, type, difficulty, etc.) when the strategy is `POOL_ONLY` or as the initial step for `HYBRID_POOL_FIRST`.
### Details:
Based on the determined `questionSourceStrategy` (from subtask 1), if `POOL_ONLY` or `HYBRID_POOL_FIRST`, call `QuestionPoolService.getRandomQuestions` with the filters derived from the worksheet generation request. Handle responses from the service, including scenarios where sufficient questions are found, insufficient questions are found, or no questions are found. Store the retrieved pool questions temporarily.

## 3. Implement AI-Powered Question Generation Logic [pending]
### Dependencies: 17.2
### Description: Integrate `AiService` (Task 4) and `QuestionStrategyFactory` (Task 8) for AI-driven question generation. This logic will be triggered if the strategy is `AI_ONLY`, or as a fallback in `HYBRID_POOL_FIRST` mode if the question pool yields an insufficient number of questions.
### Details:
If `questionSourceStrategy` is `AI_ONLY`, all requested questions should be generated via AI. If `HYBRID_POOL_FIRST` and the pool fetch (subtask 2) resulted in a deficit, calculate the number of additional questions needed. Use `QuestionStrategyFactory` to select the appropriate AI generation strategy based on question type, subject, topic, and difficulty. Invoke the `AiService` to generate the required number of questions. Apply filters as specified.

## 4. Integrate Content Validation and Type Consistency for Sourced Questions [pending]
### Dependencies: 17.3
### Description: Incorporate `ContentValidationService` (Task 9) to validate all questions, whether sourced from the pool or generated by AI. Ensure consistent handling and mapping of question types between the original request and the sourced/generated questions.
### Details:
After questions are collected (from pool, AI, or both), iterate through the combined list. For each question, invoke `ContentValidationService.validate`. Define and implement a clear strategy for handling validation failures (e.g., log the failure and discard the question, attempt to replace it if feasible, or fail the worksheet generation if too many invalid questions). Ensure that the types of the validated questions align with the worksheet specifications, potentially using `QuestionStrategyFactory` for context if AI generation was involved.

## 5. Finalize Integration, Add Robust Error Handling & Logging, and Ensure Existing Functionality [pending]
### Dependencies: 17.4
### Description: Consolidate the complete hybrid question sourcing logic. Implement comprehensive error handling for external service calls (to `QuestionPoolService`, `AiService`) and critical validation steps. Add detailed logging throughout the sourcing process. Crucially, verify that existing functionalities like progress tracking and WebSocket updates (Task 14) remain intact and operate correctly with the new changes.
### Details:
Wrap calls to `QuestionPoolService` and `AiService` in try-catch blocks to handle potential exceptions (e.g., network issues, API errors). Implement logging for key events: chosen strategy, number of questions attempted from pool, number retrieved from pool, number attempted from AI, number generated by AI, validation results for each question, and any errors encountered. Review and test existing progress update mechanisms and WebSocket message dispatches to ensure they accurately reflect the state of the hybrid generation process. Define an overall failure strategy if an insufficient number of valid questions can be sourced after all attempts.

