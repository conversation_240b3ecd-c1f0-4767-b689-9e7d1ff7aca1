# Task ID: 10
# Title: Develop Basic Worksheet Generation Workflow
# Status: done
# Dependencies: 8, 9
# Priority: high
# Description: Develop the core workflow for generating a worksheet using AI-powered content generation.
# Details:
Create a `WorksheetService` and an API endpoint (e.g., POST /worksheets/generate). This endpoint will receive parameters (subject, topic, difficulty, number of questions, question types, etc.). The service will orchestrate the process: use the `PromptService` (Task 6) to get prompts, use the `QuestionStrategyFactory` (Task 8) to get strategies, call the strategies to generate questions via the `AiService` (Task 4), validate generated content using `ContentValidationService` (Task 9), and save the generated `Worksheet` and `Question` data to the database (Task 5).

# Test Strategy:
Write integration tests using Supertest for the worksheet generation endpoint. Mock external AI calls if necessary, but test the full internal flow: receiving parameters, calling services, saving to DB. Verify that a worksheet and associated questions are created in the database with the correct structure.
