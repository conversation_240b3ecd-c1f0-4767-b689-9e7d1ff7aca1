# Task ID: 7
# Title: Implement Question Type Strategy Pattern
# Status: done
# Dependencies: 1
# Priority: medium
# Description: Implement the Strategy pattern to handle different question types.
# Details:
Define a TypeScript interface (e.g., `IQuestionGeneratorStrategy`) with a method like `generate(prompt: string, options: any): Promise<Question>`. Create concrete classes implementing this interface for different question types (e.g., `MultipleChoiceStrategy`, `FillInBlankStrategy`). This promotes modularity and allows adding new question types easily.

# Test Strategy:
Write unit tests to ensure the interface is correctly defined and that concrete strategy classes adhere to the interface contract. Test basic instantiation of strategy classes.
