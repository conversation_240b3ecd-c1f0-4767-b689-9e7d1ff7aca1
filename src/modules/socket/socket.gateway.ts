import { WebSocketGateway, WebSocketServer } from '@nestjs/websockets';
import { Server } from 'socket.io';
import { Injectable, Logger } from '@nestjs/common';

@WebSocketGateway({
  cors: {
    origin: '*',
  },
})
@Injectable()
export class SocketGateway {
  @WebSocketServer()
  server: Server;

  private logger: Logger = new Logger('SocketGateway');

  emitWorksheetGenerated(worksheetId: string, data: any) {
    this.server.emit('worksheet:generated', {
      worksheetId,
      data,
      timestamp: new Date().toISOString(),
    });
    this.logger.log(`Emitted worksheet:generated for worksheet ${worksheetId}`);
  }

  /**
   * Emits progress updates for worksheet generation and image generation
   *
   * Events emitted:
   * - 'worksheet:progress': General progress updates for worksheet generation
   *   - For question generation: includes current/total question counts
   *   - For image generation: includes imageGenerationStarted, imageGenerationProgress, or imageGenerationComplete flags
   *
   * - 'worksheet:images:complete': Specific event when all images are generated
   * - 'worksheet:images:error': Specific event when image generation encounters an error
   *
   * @param worksheetId The ID of the worksheet
   * @param currentQuestionCount Current count of questions or images processed
   * @param totalQuestionCount Total count of questions or images to process
   * @param questionResult Optional result data to include in the event
   */
  emitWorksheetProgress(worksheetId: string, currentQuestionCount: number, totalQuestionCount: number, questionResult?: any) {
    // Prevent division by zero
    const percentage = totalQuestionCount > 0
      ? Math.round((currentQuestionCount / totalQuestionCount) * 100)
      : 0;

    this.server.emit('worksheet:progress', {
      worksheetId,
      progress: {
        current: currentQuestionCount,
        total: totalQuestionCount,
        percentage: percentage,
      },
      questionResult,
      timestamp: new Date().toISOString(),
    });

    // Log different messages based on the type of progress update
    if (questionResult && questionResult.imageGenerationStarted) {
      this.logger.log(`Emitted worksheet:progress for worksheet ${worksheetId}: Image generation started`);
    } else if (questionResult && questionResult.imageGenerationProgress) {
      // Include batch information if available
      const batchInfo = questionResult.currentBatch && questionResult.totalBatches
        ? ` (batch ${questionResult.currentBatch}/${questionResult.totalBatches})`
        : '';
      this.logger.log(`Emitted worksheet:progress for worksheet ${worksheetId}: Image generation progress ${currentQuestionCount}/${totalQuestionCount}${batchInfo}`);
    } else if (questionResult && questionResult.imageGenerationComplete) {
      this.logger.log(`Emitted worksheet:progress for worksheet ${worksheetId}: Image generation complete`);
    } else if (questionResult && questionResult.imageGenerationError) {
      this.logger.log(`Emitted worksheet:progress for worksheet ${worksheetId}: Image generation error: ${questionResult.errorMessage || 'Unknown error'}`);
    } else {
      this.logger.log(`Emitted worksheet:progress for worksheet ${worksheetId}: ${currentQuestionCount}/${totalQuestionCount}`);
    }
  }
}
