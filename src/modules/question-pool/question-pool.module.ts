import { Module, forwardRef } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { TypeOrmModule } from '@nestjs/typeorm';
import { QuestionPoolService } from './question-pool.service';
import { QuestionPoolConfigService } from './question-pool-config.service';
import { QuestionPoolCacheService } from './services/question-pool-cache.service';
import { QuestionPoolMetricsService } from './services/question-pool-metrics.service';
import { QuestionPoolMetricsController } from './question-pool-metrics.controller';
import { QuestionPool, QuestionPoolSchema } from '../mongodb/schemas/question-pool.schema';
import { QuestionGeneratorCronService } from './question-generator-cron.service';
import { PromptModule } from '../prompt/prompt.module';
import { BuildPromptModule } from '../build-prompt/build-prompt.module';
import { ValidationModule } from '../validation/validation.module';
import { WorksheetModule } from '../worksheet/worksheet.module';
import { OptionType } from '../options/entities/option-type.entity';
import { OptionValue } from '../options/entities/option-value.entity';

@Module({
  imports: [
    MongooseModule.forFeature([
      { name: QuestionPool.name, schema: QuestionPoolSchema },
    ]),
    TypeOrmModule.forFeature([
      OptionType,
      OptionValue,
    ]),
    forwardRef(() => PromptModule),
    forwardRef(() => BuildPromptModule),
    forwardRef(() => WorksheetModule),
    ValidationModule,
  ],
  controllers: [QuestionPoolMetricsController],
  providers: [
    QuestionPoolService,
    QuestionPoolConfigService,
    QuestionPoolCacheService,
    QuestionPoolMetricsService,
    QuestionGeneratorCronService
  ],
  exports: [
    QuestionPoolService,
    QuestionPoolConfigService,
    QuestionPoolCacheService,
    QuestionPoolMetricsService
  ],
})
export class QuestionPoolModule {}
