/**
 * Interfaces related to worksheet generation queue processing
 */

/**
 * Data structure for worksheet generation job
 */
export interface WorksheetGenerateJobData {
  worksheetId: string;
  topic: string;
  grade: string;
}

/**
 * Question item structure from the question pool
 */
export interface QuestionItem {
  type: string;
  content: string;
  options?: any[];
  answer?: any;
  explain?: string;
  imagePrompt?: string;
  image?: string;
  subject?: string;
  parentSubject?: string;
  childSubject?: string;
  grade?: string;
  [key: string]: any;
}
