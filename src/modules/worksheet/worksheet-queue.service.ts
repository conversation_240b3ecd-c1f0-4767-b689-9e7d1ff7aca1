import { Injectable } from '@nestjs/common';
import { InjectQueue } from '@nestjs/bullmq';
import { Queue } from 'bullmq';

@Injectable()
export class WorksheetQueueService {
  constructor(
    @InjectQueue('worksheet_generate')
    private worksheetQueue: Queue,
  ) {}

  async addWorksheetGenerationJob(
    worksheetId: string,
    topic: string,
    grade: string,
    questionCount: number,
    isCustomQuestionCount?: boolean,
  ): Promise<void> {
    await this.worksheetQueue.add('generate', {
      worksheetId,
      topic,
      grade,
      questionCount,
      isCustomQuestionCount,
    });
  }
}
